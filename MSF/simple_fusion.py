#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多源降水预报融合分析
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.metrics import mean_squared_error
from sklearn.neural_network import MLPRegressor
from sklearn.preprocessing import StandardScaler
import xgboost as xgb
import warnings
import os
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def calculate_rmse(observed, predicted):
    """计算RMSE"""
    # 计算均方根误差
    rmse = np.sqrt(mean_squared_error(observed, predicted))
    return rmse

def equal_weight_average(test_data):
    """等权平均法"""
    forecast_cols = ['E2', 'China', 'NOAA']
    equal_weight_pred = test_data[forecast_cols].mean(axis=1)
    rmse = calculate_rmse(test_data['real_tp'], equal_weight_pred)
    return equal_weight_pred, rmse

def rmse_inverse_weight_average(train_data, test_data):
    """RMSE倒数加权平均法"""
    forecast_cols = ['E2', 'China', 'NOAA']
    
    # 计算各产品的RMSE
    rmse_values = []
    for col in forecast_cols:
        rmse = np.sqrt(mean_squared_error(train_data['real_tp'], train_data[col]))
        rmse_values.append(rmse)
    
    # 计算权重
    weights = [1/rmse if rmse > 0 else 0 for rmse in rmse_values]
    weights = np.array(weights) / np.sum(weights)
    
    # 加权平均
    weighted_pred = np.zeros(len(test_data))
    for i, col in enumerate(forecast_cols):
        weighted_pred += weights[i] * test_data[col]
    
    rmse = calculate_rmse(test_data['real_tp'], weighted_pred)
    return weighted_pred, rmse

def xgboost_fusion(train_data, test_data):
    """XGBoost融合方法"""
    forecast_cols = ['E2', 'China', 'NOAA']
    
    X_train = train_data[forecast_cols].values
    y_train = train_data['real_tp'].values
    X_test = test_data[forecast_cols].values
    
    # 简化的XGBoost模型
    xgb_model = xgb.XGBRegressor(
        n_estimators=100,
        max_depth=5,
        learning_rate=0.1,
        random_state=42
    )
    xgb_model.fit(X_train, y_train)
    
    xgb_pred = xgb_model.predict(X_test)
    rmse = calculate_rmse(test_data['real_tp'], xgb_pred)
    
    return xgb_pred, rmse

def mlp_fusion(train_data, test_data):
    """MLP融合方法"""
    forecast_cols = ['E2', 'China', 'NOAA']
    
    X_train = train_data[forecast_cols].values
    y_train = train_data['real_tp'].values
    X_test = test_data[forecast_cols].values
    
    # 标准化
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    # 简化的MLP模型
    mlp = MLPRegressor(
        hidden_layer_sizes=(100,),
        activation='relu',
        alpha=0.001,
        learning_rate_init=0.01,
        max_iter=1000,
        random_state=42
    )
    mlp.fit(X_train_scaled, y_train)
    
    mlp_pred = mlp.predict(X_test_scaled)
    rmse = calculate_rmse(test_data['real_tp'], mlp_pred)
    
    return mlp_pred, rmse

def evaluate_individual_products(test_data):
    """评估单个产品的性能"""
    forecast_cols = ['E2', 'China', 'NOAA']
    individual_rmse = {}
    
    for col in forecast_cols:
        rmse = calculate_rmse(test_data['real_tp'], test_data[col])
        individual_rmse[col] = rmse
        
    return individual_rmse

def plot_radar_chart(results, basin_name, save_path=None):
    """绘制雷达图"""
    methods = list(results['individual'].keys()) + ['等权平均', 'RMSE加权', 'XGBoost', 'MLP']
    rmse_values = list(results['individual'].values()) + [
        results['equal_weight'],
        results['rmse_weight'],
        results['xgboost'],
        results['mlp']
    ]
    
    # 设置雷达图
    angles = np.linspace(0, 2 * np.pi, len(methods), endpoint=False).tolist()
    rmse_values += rmse_values[:1]  # 闭合图形
    angles += angles[:1]
    
    fig, ax = plt.subplots(figsize=(10, 8), subplot_kw=dict(projection='polar'))
    
    # 绘制雷达图
    ax.plot(angles, rmse_values, 'o-', linewidth=2, label='RMSE值')
    ax.fill(angles, rmse_values, alpha=0.25)
    
    # 设置标签
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(methods, fontsize=12)
    # 动态设置y轴范围
    max_rmse = max(rmse_values[:-1])  # 不包括最后一个重复值
    ax.set_ylim(0, max_rmse * 1.1)
    ax.grid(True)
    ax.grid(True)
    
    # 添加标题
    plt.title(f'{basin_name}流域多源融合方法RMSE对比', fontsize=16, pad=20)
    
    # 添加图例
    plt.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"雷达图已保存到: {save_path}")
    
    plt.show()

def analyze_basin(data_path):
    """分析单个流域"""
    # 加载数据
    data = pd.read_csv(data_path)
    data['valid_time'] = pd.to_datetime(data['valid_time'])
    data = data.sort_values('valid_time').reset_index(drop=True)
    
    # 数据划分
    total_len = len(data)
    train_end = int(total_len * 0.5)
    test_start = int(total_len * 0.6)
    
    train_data = data.iloc[:train_end]
    test_data = data.iloc[test_start:]
    
    print(f"训练集: {len(train_data)} 条记录")
    print(f"测试集: {len(test_data)} 条记录")
    
    # 评估单个产品
    individual_rmse = evaluate_individual_products(test_data)
    print("\n单个产品RMSE:")
    for product, rmse in individual_rmse.items():
        print(f"{product}: {rmse:.4f}")
    
    # 等权平均
    print("\n计算等权平均...")
    equal_weight_pred, equal_weight_rmse = equal_weight_average(test_data)
    print(f"等权平均RMSE: {equal_weight_rmse:.4f}")
    
    # RMSE倒数加权平均
    print("计算RMSE倒数加权平均...")
    rmse_weight_pred, rmse_weight_rmse = rmse_inverse_weight_average(train_data, test_data)
    print(f"RMSE倒数加权平均RMSE: {rmse_weight_rmse:.4f}")
    
    # XGBoost融合
    print("训练XGBoost模型...")
    xgb_pred, xgb_rmse = xgboost_fusion(train_data, test_data)
    print(f"XGBoost RMSE: {xgb_rmse:.4f}")
    
    # MLP融合
    print("训练MLP模型...")
    mlp_pred, mlp_rmse = mlp_fusion(train_data, test_data)
    print(f"MLP RMSE: {mlp_rmse:.4f}")
    
    # 保存结果
    results = {
        'individual': individual_rmse,
        'equal_weight': equal_weight_rmse,
        'rmse_weight': rmse_weight_rmse,
        'xgboost': xgb_rmse,
        'mlp': mlp_rmse
    }
    
    return results

def main():
    """主函数"""
    print("开始多源融合分析...")
    # 流域数据文件列表
    basins = ['chengkou', 'dongxi', 'guojia', 'lianghe', 'mituo']
    
    # 创建结果目录
    results_dir = 'MSF/results'
    os.makedirs(results_dir, exist_ok=True)
    
    all_results = {}
    
    for basin in basins:
        print(f"\n{'='*50}")
        print(f"分析 {basin} 流域")
        print(f"{'='*50}")
        
        # 分析流域
        data_path = f'MSF/data/{basin}.csv'
        results = analyze_basin(data_path)
        all_results[basin] = results
        
        # 绘制雷达图
        radar_path = f'{results_dir}/{basin}_radar_chart.png'
        plot_radar_chart(results, basin, radar_path)
        
        # 保存结果
        results_df = pd.DataFrame([
            {'方法': 'E2', 'RMSE': results['individual']['E2']},
            {'方法': 'China', 'RMSE': results['individual']['China']},
            {'方法': 'NOAA', 'RMSE': results['individual']['NOAA']},
            {'方法': '等权平均', 'RMSE': results['equal_weight']},
            {'方法': 'RMSE加权', 'RMSE': results['rmse_weight']},
            {'方法': 'XGBoost', 'RMSE': results['xgboost']},
            {'方法': 'MLP', 'RMSE': results['mlp']}
        ])
        
        results_path = f'{results_dir}/{basin}_results.csv'
        results_df.to_csv(results_path, index=False, encoding='utf-8-sig')
        print(f"结果已保存到: {results_path}")
    
    # 汇总所有流域的结果
    print(f"\n{'='*50}")
    print("所有流域结果汇总")
    print(f"{'='*50}")
    
    summary_data = []
    for basin, results in all_results.items():
        summary_data.append({
            '流域': basin,
            'E2': results['individual']['E2'],
            'China': results['individual']['China'],
            'NOAA': results['individual']['NOAA'],
            '等权平均': results['equal_weight'],
            'RMSE加权': results['rmse_weight'],
            'XGBoost': results['xgboost'],
            'MLP': results['mlp']
        })
    
    summary_df = pd.DataFrame(summary_data)
    summary_path = f'{results_dir}/all_basins_summary.csv'
    summary_df.to_csv(summary_path, index=False, encoding='utf-8-sig')
    print(f"汇总结果已保存到: {summary_path}")
    
    # 打印汇总表格
    print("\n各流域RMSE结果汇总:")
    print(summary_df.to_string(index=False, float_format='%.4f'))
    
    # 计算平均性能
    print("\n各方法平均RMSE:")
    avg_performance = summary_df.drop('流域', axis=1).mean()
    for method, avg_rmse in avg_performance.items():
        print(f"{method}: {avg_rmse:.4f}")

if __name__ == "__main__":
    main()
