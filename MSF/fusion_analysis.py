#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多源降水预报融合分析
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.metrics import mean_squared_error
from sklearn.neural_network import MLPRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import GridSearchCV
import xgboost as xgb
import warnings
import os
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class MultiSourceFusion:
    def __init__(self, data_path):
        self.data_path = data_path
        self.data = None
        self.scaler = StandardScaler()
        self.results = {}
        
    def load_data(self):
        self.data = pd.read_csv(self.data_path)
        self.data['valid_time'] = pd.to_datetime(self.data['valid_time'])
        self.data = self.data.sort_values('valid_time').reset_index(drop=True)
        print(f"数据加载完成: {len(self.data)} 条记录")
        return self.data
    
    def calculate_rmse(self, observed, predicted):
        """计算RMSE"""
        # 计算均方根误差
        rmse = np.sqrt(mean_squared_error(observed, predicted))
        return rmse
    
    def equal_weight_average(self, test_data):
        """等权平均法"""
        forecast_cols = ['E2', 'China', 'NOAA']
        equal_weight_pred = test_data[forecast_cols].mean(axis=1)
        rmse = self.calculate_rmse(test_data['real_tp'], equal_weight_pred)
        return equal_weight_pred, rmse
    
    def rmse_inverse_weight_average(self, train_data, test_data):
        """RMSE倒数加权平均法"""
        forecast_cols = ['E2', 'China', 'NOAA']
        
        # 在训练集上计算各产品的RMSE
        rmse_values = []
        for col in forecast_cols:
            rmse = np.sqrt(mean_squared_error(train_data['real_tp'], train_data[col]))
            rmse_values.append(rmse)
        
        # 计算权重（RMSE的倒数）
        weights = [1/rmse if rmse > 0 else 0 for rmse in rmse_values]
        weights = np.array(weights) / np.sum(weights)
        
        # 加权平均
        weighted_pred = np.zeros(len(test_data))
        for i, col in enumerate(forecast_cols):
            weighted_pred += weights[i] * test_data[col]
        
        rmse = self.calculate_rmse(test_data['real_tp'], weighted_pred)
        return weighted_pred, rmse
    
    def xgboost_fusion(self, train_data, val_data, test_data):
        """XGBoost融合方法"""
        forecast_cols = ['E2', 'China', 'NOAA']
        
        # 准备训练数据
        X_train = train_data[forecast_cols].values
        y_train = train_data['real_tp'].values
        X_test = test_data[forecast_cols].values
        
        # 超参数网格搜索
        param_grid = {
            'n_estimators': [50, 100],
            'max_depth': [3, 5],
            'learning_rate': [0.1, 0.2]
        }
        
        xgb_model = xgb.XGBRegressor(random_state=42, objective='reg:squarederror')
        grid_search = GridSearchCV(
            xgb_model, param_grid, cv=3, scoring='neg_mean_squared_error', 
            n_jobs=-1, verbose=0
        )
        grid_search.fit(X_train, y_train)
        
        # 使用最佳参数训练模型
        best_model = grid_search.best_estimator_
        best_model.fit(X_train, y_train)
        
        # 预测
        xgb_pred = best_model.predict(X_test)
        rmse = self.calculate_rmse(test_data['real_tp'], xgb_pred)
        
        return xgb_pred, rmse, best_model
    
    def mlp_fusion(self, train_data, val_data, test_data):
        """MLP融合方法"""
        forecast_cols = ['E2', 'China', 'NOAA']
        
        # 准备数据
        X_train = train_data[forecast_cols].values
        y_train = train_data['real_tp'].values
        X_test = test_data[forecast_cols].values
        
        # 标准化
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)
        
        # 超参数网格搜索
        param_grid = {
            'hidden_layer_sizes': [(50,), (100,)],
            'activation': ['relu'],
            'alpha': [0.001, 0.01],
            'learning_rate_init': [0.01, 0.1]
        }
        
        mlp = MLPRegressor(random_state=42, max_iter=1000, early_stopping=True)
        grid_search = GridSearchCV(
            mlp, param_grid, cv=3, scoring='neg_mean_squared_error',
            n_jobs=-1, verbose=0
        )
        grid_search.fit(X_train_scaled, y_train)
        
        # 使用最佳参数训练模型
        best_model = grid_search.best_estimator_
        best_model.fit(X_train_scaled, y_train)
        
        # 预测
        mlp_pred = best_model.predict(X_test_scaled)
        rmse = self.calculate_rmse(test_data['real_tp'], mlp_pred)
        
        return mlp_pred, rmse, best_model
    
    def evaluate_individual_products(self, test_data):
        """评估单个产品的性能"""
        forecast_cols = ['E2', 'China', 'NOAA']
        individual_rmse = {}
        
        for col in forecast_cols:
            rmse = self.calculate_rmse(test_data['real_tp'], test_data[col])
            individual_rmse[col] = rmse
            
        return individual_rmse
    
    def run_analysis(self):
        """运行完整的融合分析"""
        # 加载数据
        self.load_data()
        
        # 数据划分
        total_len = len(self.data)
        train_end = int(total_len * 0.5)
        val_end = int(total_len * 0.6)
        test_start = int(total_len * 0.6)
        
        train_data = self.data.iloc[:train_end]
        val_data = self.data.iloc[train_end:val_end]
        test_data = self.data.iloc[test_start:]
        
        print(f"训练集: {len(train_data)} 条记录")
        print(f"验证集: {len(val_data)} 条记录")
        print(f"测试集: {len(test_data)} 条记录")
        
        # 评估单个产品
        individual_rmse = self.evaluate_individual_products(test_data)
        print("\n单个产品RMSE:")
        for product, rmse in individual_rmse.items():
            print(f"{product}: {rmse:.4f}")
        
        # 等权平均
        print("\n计算等权平均...")
        equal_weight_pred, equal_weight_rmse = self.equal_weight_average(test_data)
        print(f"等权平均RMSE: {equal_weight_rmse:.4f}")
        
        # RMSE倒数加权平均
        print("计算RMSE倒数加权平均...")
        rmse_weight_pred, rmse_weight_rmse = self.rmse_inverse_weight_average(train_data, test_data)
        print(f"RMSE倒数加权平均RMSE: {rmse_weight_rmse:.4f}")
        
        # XGBoost融合
        print("训练XGBoost模型...")
        xgb_pred, xgb_rmse, xgb_model = self.xgboost_fusion(train_data, val_data, test_data)
        print(f"XGBoost RMSE: {xgb_rmse:.4f}")
        
        # MLP融合
        print("训练MLP模型...")
        mlp_pred, mlp_rmse, mlp_model = self.mlp_fusion(train_data, val_data, test_data)
        print(f"MLP RMSE: {mlp_rmse:.4f}")
        
        # 保存结果
        self.results = {
            'individual': individual_rmse,
            'equal_weight': equal_weight_rmse,
            'rmse_weight': rmse_weight_rmse,
            'xgboost': xgb_rmse,
            'mlp': mlp_rmse
        }
        
        return self.results
    
    def plot_radar_chart(self, save_path=None):
        """绘制雷达图"""
        if not self.results:
            print("请先运行分析")
            return
        
        # 准备数据
        methods = list(self.results['individual'].keys()) + ['等权平均', 'RMSE加权', 'XGBoost', 'MLP']
        rmse_values = list(self.results['individual'].values()) + [
            self.results['equal_weight'],
            self.results['rmse_weight'],
            self.results['xgboost'],
            self.results['mlp']
        ]
        
        # 设置雷达图
        angles = np.linspace(0, 2 * np.pi, len(methods), endpoint=False).tolist()
        rmse_values += rmse_values[:1]  # 闭合图形
        angles += angles[:1]
        
        fig, ax = plt.subplots(figsize=(10, 8), subplot_kw=dict(projection='polar'))
        
        # 绘制雷达图
        ax.plot(angles, rmse_values, 'o-', linewidth=2, label='RMSE值')
        ax.fill(angles, rmse_values, alpha=0.25)
        
        # 设置标签
        ax.set_xticks(angles[:-1])
        ax.set_xticklabels(methods, fontsize=12)
        # 动态设置y轴范围
        max_rmse = max(rmse_values[:-1])  # 不包括最后一个重复值
        ax.set_ylim(0, max_rmse * 1.1)
        ax.grid(True)
        
        # 添加标题
        basin_name = self.data_path.split('/')[-1].replace('.csv', '')
        plt.title(f'{basin_name}流域多源融合方法RMSE对比', fontsize=16, pad=20)
        
        # 添加图例
        plt.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"雷达图已保存到: {save_path}")
        
        plt.show()
        
    def save_results(self, save_path):
        """保存结果到CSV文件"""
        if not self.results:
            print("请先运行分析")
            return
            
        results_df = pd.DataFrame([
            {'方法': 'E2', 'RMSE': self.results['individual']['E2']},
            {'方法': 'China', 'RMSE': self.results['individual']['China']},
            {'方法': 'NOAA', 'RMSE': self.results['individual']['NOAA']},
            {'方法': '等权平均', 'RMSE': self.results['equal_weight']},
            {'方法': 'RMSE加权', 'RMSE': self.results['rmse_weight']},
            {'方法': 'XGBoost', 'RMSE': self.results['xgboost']},
            {'方法': 'MLP', 'RMSE': self.results['mlp']}
        ])
        
        results_df.to_csv(save_path, index=False, encoding='utf-8-sig')
        print(f"结果已保存到: {save_path}")

def main():
    """主函数"""
    # 流域数据文件列表
    basins = ['chengkou', 'dongxi', 'guojia', 'lianghe', 'mituo']
    
    # 创建结果目录
    results_dir = 'MSF/results'
    os.makedirs(results_dir, exist_ok=True)
    
    all_results = {}
    
    for basin in basins:
        print(f"\n{'='*50}")
        print(f"分析 {basin} 流域")
        print(f"{'='*50}")
        
        # 创建分析器
        data_path = f'MSF/data/{basin}.csv'
        analyzer = MultiSourceFusion(data_path)
        
        # 运行分析
        results = analyzer.run_analysis()
        all_results[basin] = results
        
        # 绘制雷达图
        radar_path = f'{results_dir}/{basin}_radar_chart.png'
        analyzer.plot_radar_chart(radar_path)
        
        # 保存结果
        results_path = f'{results_dir}/{basin}_results.csv'
        analyzer.save_results(results_path)
    
    # 汇总所有流域的结果
    print(f"\n{'='*50}")
    print("所有流域结果汇总")
    print(f"{'='*50}")
    
    summary_data = []
    for basin, results in all_results.items():
        summary_data.append({
            '流域': basin,
            'E2': results['individual']['E2'],
            'China': results['individual']['China'],
            'NOAA': results['individual']['NOAA'],
            '等权平均': results['equal_weight'],
            'RMSE加权': results['rmse_weight'],
            'XGBoost': results['xgboost'],
            'MLP': results['mlp']
        })
    
    summary_df = pd.DataFrame(summary_data)
    summary_path = f'{results_dir}/all_basins_summary.csv'
    summary_df.to_csv(summary_path, index=False, encoding='utf-8-sig')
    print(f"汇总结果已保存到: {summary_path}")
    
    # 打印汇总表格
    print("\n各流域RMSE结果汇总:")
    print(summary_df.to_string(index=False, float_format='%.4f'))
    
    # 计算平均性能
    print("\n各方法平均RMSE:")
    avg_performance = summary_df.drop('流域', axis=1).mean()
    for method, avg_rmse in avg_performance.items():
        print(f"{method}: {avg_rmse:.4f}")

if __name__ == "__main__":
    main()
